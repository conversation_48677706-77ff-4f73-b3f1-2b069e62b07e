# 看板需求实现方案 v1.0

## 📋 项目概述

基于现有技术栈（Vue 3 + TypeScript + Ant Design Vue + Vite），构建一个现代化的数据可视化看板平台，支持多Tab页展示、图表组件化、数据下探交互、PDF导出等核心功能。

## 🎯 核心需求

### 功能需求
1. **多Tab看板**：5个Tab页，每个Tab包含多个图表组
2. **图表组结构**：图表组件 + 数据展示内容区 = 完整组结构
3. **PDF导出**：包含图表组和数据展示的完整内容导出
4. **筛选功能**：顶部自定义筛选条件，支持业务数据联动
5. **交互体验**：PC端响应式设计，预留拖拽布局功能
6. **图表需求**：2D图表展示，支持数据下探交互

### 性能需求
1. **图表懒加载**：按需加载图表组件，提升页面性能
2. **图表库分包**：图表库单独打包，优化加载速度
3. **响应式设计**：适配不同屏幕尺寸的PC端设备

## 🛠️ 技术选型

### 核心技术栈
```
前端框架：Vue 3 + TypeScript + Vite
UI组件库：Ant Design Vue 4.2.6
状态管理：Vue 3 组合式API（父子组件通信）
路由管理：Vue Router 4
图表库：AntV G2 (最新版本)
PDF导出：html2canvas + jsPDF
拖拽功能：Vue Draggable Next + SortableJS
构建工具：Vite（代码分割 + Tree Shaking）
```

### AntV G2 安装说明
根据官方文档调研，正确的安装方式为： <mcreference link="https://www.npmjs.com/package/@antv/g2" index="1">1</mcreference>
```bash
# 只需安装核心包
pnpm install @antv/g2
```

**重要说明**：
- `@antv/g2-components` 包不存在，无需安装
- G2 5.x 版本已经内置了所有必要的组件和功能 <mcreference link="https://g2.antv.antgroup.com/en/manual/quick-start" index="1">1</mcreference>
- 支持 Tree Shaking，可按需引入功能模块 <mcreference link="https://g2.antv.antgroup.com/en/manual/extra-topics/bundle" index="2">2</mcreference>
- 完全兼容 TypeScript，提供完整的类型定义

### AntV G2 官方文档
- **官方网站**：https://g2.antv.antgroup.com/
- **API文档**：https://g2.antv.antgroup.com/api/
- **示例教程**：https://g2.antv.antgroup.com/examples/
- **GitHub仓库**：https://github.com/antvis/G2

### 图表库选型对比

#### ECharts
**优点**：功能最全面、数据下探能力强、中文文档完善、导出功能完善  
**缺点**：包体积较大、学习曲线陡峭、项目曾移除过

#### Chart.js
**优点**：轻量级(37KB)、简单易用、性能优秀、响应式设计好  
**缺点**：图表类型有限、数据下探需额外开发、高级交互功能不足

#### AntV G2 ⭐ **最终选择**
**优点**：
- 与项目技术栈完美融合（Ant Design Vue + TypeScript）
- 基于图形语法，配置简洁直观
- 数据下探交互能力强，内置drill-down支持
- 支持按需加载和Tree Shaking
- 阿里生态产品，长期维护有保障
- 中文文档完善，移动端适配优秀

**缺点**：相对较新、需要学习图形语法概念

## 🏗️ 系统架构设计

### 组件架构
```
src/views/dashboard/statistics/
├── StatisticsDashboard.vue      # 统计看板页面
├── components/                  # 组件目录
│   ├── FilterPanel.vue          # 全局筛选器面板
│   ├── TabContainer.vue         # Tab容器
│   ├── ChartGroup.vue           # 三区域图表组（主组件）
│   ├── chart-sections/          # 图表组三区域子组件
│   │   ├── MetricsSection.vue   # 数据指标区域
│   │   ├── LocalFilters.vue     # 局部筛选区域
│   │   └── ChartDisplay.vue     # 图表展示区域
│   ├── charts/                  # G2图表组件
│   │   ├── G2Chart.vue          # G2图表基础组件（函数式语法）
│   │   ├── BarChart.vue         # 柱状图组件
│   │   ├── LineChart.vue        # 折线图组件
│   │   ├── PieChart.vue         # 饼图组件
│   │   └── AreaChart.vue        # 面积图组件
│   ├── ui/                      # UI组件
│   │   ├── MetricCard.vue       # 指标卡片
│   │   ├── ChartSkeleton.vue    # 图表骨架屏
│   │   └── ResponsiveGrid.vue   # 响应式网格容器
│   └── filters/                 # 筛选器组件
│       ├── DateRangeFilter.vue  # 日期范围筛选器
│       ├── SelectFilter.vue     # 下拉选择筛选器
│       └── InputFilter.vue      # 输入框筛选器
├── hooks/                       # hooks
│   ├── useStatisticDashboard.ts # 统计看板状态管理
│   ├── useChartData.ts          # 图表数据管理
│   ├── useFilters.ts            # 筛选器管理
│   ├── useMetrics.ts            # 指标计算管理
│   ├── useResponsive.ts         # 响应式布局管理
│   └── useG2Chart.ts            # G2图表函数式语法封装
├── types/                       # 类型定义
│   ├── statisticDashboard.ts    # 统计看板类型定义
│   ├── chartConfig.ts           # 图表配置类型
│   ├── metrics.ts               # 指标相关类型
│   └── filters.ts               # 筛选器类型
├── utils/                       # 工具函数
│   ├── chartRenderer.ts         # G2图表渲染工具
│   ├── metricsCalculator.ts     # 指标计算工具
│   ├── dataProcessor.ts         # 数据处理工具
│   └── responsive.ts            # 响应式工具
├── mock/                        # 模拟数据
│   ├── data.ts                  # 统计看板模拟数据
│   ├── metrics.ts               # 指标模拟数据
│   └── chartConfigs.ts          # 图表配置模拟数据
└── styles/                      # 样式文件
    ├── statisticDashboard.less  # 看板样式
    ├── chartGroup.less          # 图表组样式
    ├── metrics.less             # 指标区域样式
    └── responsive.less          # 响应式样式
```

### 数据流设计
```typescript
// 父组件状态管理（使用 Vue 3 组合式API）
interface DashboardState {
  // 筛选条件
  filters: {
    dateRange: [string, string]
    region: string[]
    customFilters: Record<string, any>
  }
  
  // Tab配置
  tabs: TabConfig[]
  activeTab: string
  
  // 图表数据
  chartData: Record<string, any>
  
  // 拖拽状态
  isDragging: boolean
  draggedItem: string | null
  
  // 导出状态
  isExporting: boolean
}

// 父子组件通信方式
// 1. Props 向下传递数据
// 2. Emits 向上传递事件
// 3. provide/inject 跨层级通信
// 4. ref 直接访问子组件实例
```

### 配置化设计
```typescript
// 指标数据接口
interface MetricData {
  key: string
  label: string
  value: string | number
  format?: string
  trend?: 'up' | 'down' | 'stable'
  trendIcon?: string
  change?: string
  previousValue?: number
}

// 局部筛选器配置
interface LocalFilterConfig {
  key: string
  component: string // 'a-select' | 'a-date-picker' | 'a-input' 等
  span?: number
  props?: Record<string, any>
  value?: any
}

// 图表配置接口（支持三区域布局）
interface ChartConfig {
  id: string
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter'
  title: string
  dataSource: string

  // 数据字段映射
  xField: string
  yField: string
  colorField?: string
  valueField?: string // 用于饼图等
  categoryField?: string // 用于饼图等

  // 三区域配置
  showMetrics?: boolean // 是否显示数据指标区域
  showLocalFilters?: boolean // 是否显示局部筛选区域

  // 指标配置
  metrics?: MetricConfig[]

  // 局部筛选配置
  localFilters?: LocalFilterConfig[]

  // 数据下探配置
  drillDown?: DrillDownConfig

  // 布局配置
  size: { width: number; height: number }
  position: { x: number; y: number }

  // 响应式配置
  responsive?: {
    breakpoints: {
      xs?: Partial<ChartConfig>
      sm?: Partial<ChartConfig>
      md?: Partial<ChartConfig>
      lg?: Partial<ChartConfig>
      xl?: Partial<ChartConfig>
    }
  }
}

// 指标配置接口
interface MetricConfig {
  key: string
  label: string
  field: string
  aggregation: 'sum' | 'avg' | 'count' | 'max' | 'min'
  format?: string
  showTrend?: boolean
  compareField?: string // 用于计算趋势
}

// Tab配置接口
interface TabConfig {
  id: string
  name: string
  charts: ChartConfig[]
  layout: 'grid' | 'flex' | 'custom'

  // 响应式布局配置
  responsive?: {
    columns: {
      xs: number
      sm: number
      md: number
      lg: number
      xl: number
    }
    gutter: number
  }
}

// 数据下探配置
interface DrillDownConfig {
  enabled: boolean
  levels: DrillDownLevel[]
  maxDepth?: number
}

interface DrillDownLevel {
  field: string
  label: string
  dataSource?: string
  filters?: Record<string, any>
}
```

## � G2 函数式语法与三区域图表组设计

### G2 函数式编程语法优势

#### 1. 链式调用，语义清晰
```typescript
// 函数式语法 - 推荐使用
chart
  .interval()
  .data(data)
  .encode('x', 'category')
  .encode('y', 'value')
  .encode('color', 'series')
  .scale('y', { nice: true })
  .interaction('elementHighlight')
  .interaction('tooltip', { shared: true })

// 对比：选项式语法
chart.options({
  type: 'interval',
  data: data,
  encode: { x: 'category', y: 'value', color: 'series' },
  scale: { y: { nice: true } },
  interaction: [
    { type: 'elementHighlight' },
    { type: 'tooltip', shared: true }
  ]
})
```

#### 2. 动态更新更灵活
```typescript
// 函数式语法支持动态链式更新
const updateChart = (newData: any[], chartType: string) => {
  chart.clear()

  // 根据类型动态构建图表
  const mark = chart[chartType]() // 'line' | 'interval' | 'point' 等

  mark
    .data(newData)
    .encode('x', xField.value)
    .encode('y', yField.value)

  // 条件性添加配置
  if (colorField.value) {
    mark.encode('color', colorField.value)
  }

  if (showAnimation.value) {
    mark.encode('enterDuration', 1000)
  }

  chart.render()
}
```

#### 3. 更好的TypeScript支持
```typescript
// 函数式语法提供更好的类型推断
import { Chart } from '@antv/g2'

const chart = new Chart({ container: 'container' })

// TypeScript 能够准确推断每个方法的返回类型
chart
  .interval() // 返回 Interval Mark
  .data(data) // 返回 Interval Mark，支持链式调用
  .encode('x', 'category') // 类型安全的字段映射
  .style('fill', (d) => d.value > 100 ? 'red' : 'blue') // 函数式样式
```

### 三区域图表组响应式设计

#### 1. 响应式布局策略
```typescript
// 响应式配置接口
interface ResponsiveConfig {
  breakpoints: {
    xs: { maxWidth: 575 }    // 手机
    sm: { maxWidth: 767 }    // 平板竖屏
    md: { maxWidth: 991 }    // 平板横屏
    lg: { maxWidth: 1199 }   // 小屏电脑
    xl: { minWidth: 1200 }   // 大屏电脑
  }

  layouts: {
    xs: {
      metricsColumns: 2,
      filtersPerRow: 1,
      chartHeight: 250
    }
    sm: {
      metricsColumns: 3,
      filtersPerRow: 2,
      chartHeight: 300
    }
    md: {
      metricsColumns: 4,
      filtersPerRow: 3,
      chartHeight: 350
    }
    lg: {
      metricsColumns: 5,
      filtersPerRow: 4,
      chartHeight: 400
    }
    xl: {
      metricsColumns: 6,
      filtersPerRow: 4,
      chartHeight: 450
    }
  }
}
```

#### 2. 响应式Hook实现
```typescript
// useResponsive.ts
import { ref, onMounted, onUnmounted } from 'vue'

export function useResponsive() {
  const currentBreakpoint = ref<string>('xl')
  const screenWidth = ref<number>(window.innerWidth)

  const breakpoints = {
    xs: 575,
    sm: 767,
    md: 991,
    lg: 1199
  }

  const updateBreakpoint = () => {
    screenWidth.value = window.innerWidth

    if (screenWidth.value <= breakpoints.xs) {
      currentBreakpoint.value = 'xs'
    } else if (screenWidth.value <= breakpoints.sm) {
      currentBreakpoint.value = 'sm'
    } else if (screenWidth.value <= breakpoints.md) {
      currentBreakpoint.value = 'md'
    } else if (screenWidth.value <= breakpoints.lg) {
      currentBreakpoint.value = 'lg'
    } else {
      currentBreakpoint.value = 'xl'
    }
  }

  onMounted(() => {
    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateBreakpoint)
  })

  return {
    currentBreakpoint,
    screenWidth,
    isMobile: computed(() => currentBreakpoint.value === 'xs'),
    isTablet: computed(() => ['xs', 'sm'].includes(currentBreakpoint.value)),
    isDesktop: computed(() => ['lg', 'xl'].includes(currentBreakpoint.value))
  }
}
```

#### 3. 三区域自适应布局
```vue
<template>
  <div class="chart-group" :class="`breakpoint-${currentBreakpoint}`">
    <!-- 数据指标区域 - 响应式网格 -->
    <div
      v-if="config.showMetrics"
      class="metrics-section"
      :style="metricsGridStyle"
    >
      <MetricCard
        v-for="metric in metrics"
        :key="metric.key"
        :data="metric"
        :compact="isMobile"
      />
    </div>

    <!-- 局部筛选区域 - 响应式排列 -->
    <div
      v-if="config.showLocalFilters"
      class="filters-section"
    >
      <a-row :gutter="[8, 8]">
        <a-col
          v-for="filter in localFilters"
          :key="filter.key"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          :xl="6"
        >
          <component
            :is="filter.component"
            v-model:value="filter.value"
            v-bind="getFilterProps(filter)"
            size="small"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 图表展示区域 - 自适应高度 -->
    <div
      class="chart-section"
      :style="{ height: `${chartHeight}px` }"
    >
      <G2Chart
        :config="chartConfig"
        :data="filteredData"
        :height="chartHeight"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useResponsive } from '@/hooks/useResponsive'

const { currentBreakpoint, isMobile, isTablet } = useResponsive()

// 响应式指标网格样式
const metricsGridStyle = computed(() => {
  const columns = {
    xs: 2,
    sm: 3,
    md: 4,
    lg: 5,
    xl: 6
  }[currentBreakpoint.value]

  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gap: isMobile.value ? '8px' : '12px'
  }
})

// 响应式图表高度
const chartHeight = computed(() => {
  const heights = {
    xs: 250,
    sm: 300,
    md: 350,
    lg: 400,
    xl: 450
  }
  return heights[currentBreakpoint.value]
})

// 响应式筛选器属性
const getFilterProps = (filter: LocalFilterConfig) => {
  return {
    ...filter.props,
    size: isMobile.value ? 'small' : 'middle',
    style: {
      width: '100%',
      ...filter.props?.style
    }
  }
}
</script>

<style lang="less" scoped>
.chart-group {
  .metrics-section {
    margin-bottom: 16px;

    // 移动端优化
    &.breakpoint-xs {
      margin-bottom: 12px;
    }
  }

  .filters-section {
    margin-bottom: 16px;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;

    // 移动端优化
    &.breakpoint-xs {
      padding: 8px;
      margin-bottom: 12px;
    }
  }

  .chart-section {
    position: relative;
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
  }
}
</style>
```

## �🔧 AntV G2 与拖拽布局兼容性

### 兼容性分析结果
**结论：完全兼容，无重大技术障碍**

#### 1. 渲染机制兼容性 ✅
- **G2渲染方式**：基于Canvas渲染，图表内容绘制在Canvas元素上
- **拖拽操作对象**：操作的是图表容器DOM元素，而非Canvas内部内容
- **结论**：两者操作层级不同，无直接冲突

#### 2. 事件处理机制 ⚠️ 需要设计
**解决方案**：
- 设置专用拖拽手柄区域，避免在图表内容区触发拖拽
- 使用CSS `pointer-events` 属性进行事件隔离
- 精确的事件目标判断和委托

#### 3. 性能优化策略
- 拖拽期间暂停图表自动更新和动画
- 使用CSS transform进行拖拽动画，避免layout重排
- 拖拽结束后统一触发图表resize和重渲染

### 技术实现方案
```typescript
// 事件隔离机制
const isDragging = ref(false)

watchEffect(() => {
  if (isDragging.value) {
    chartContainer.style.pointerEvents = 'none'
  } else {
    chartContainer.style.pointerEvents = 'auto'
    chart.resize() // 重新渲染图表
  }
})
```

## 📊 核心功能实现

### 1. 图表组件设计
```vue
<template>
  <div class="chart-widget" :class="{ 'is-dragging': isDragging }">
    <!-- 拖拽手柄 -->
    <div class="drag-handle" v-if="draggable">
      <Icon type="drag" />
      <span class="chart-title">{{ config.title }}</span>
    </div>

    <!-- 数据指标区域（可选） -->
    <div class="metrics-section" v-if="config.showMetrics">
      <div class="metrics-grid">
        <div
          v-for="metric in metrics"
          :key="metric.key"
          class="metric-card"
        >
          <div class="metric-value">{{ metric.value }}</div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-trend" :class="metric.trend">
            <Icon :type="metric.trendIcon" />
            {{ metric.change }}
          </div>
        </div>
      </div>
    </div>

    <!-- 局部筛选区域（可选） -->
    <div class="local-filters" v-if="config.showLocalFilters">
      <a-row :gutter="8">
        <a-col
          v-for="filter in localFilters"
          :key="filter.key"
          :span="filter.span || 6"
        >
          <component
            :is="filter.component"
            v-model:value="filter.value"
            v-bind="filter.props"
            @change="handleLocalFilterChange"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 图表展示区 -->
    <div class="chart-container" ref="chartRef">
      <Suspense>
        <template #default>
          <G2Chart
            :config="config"
            :data="chartData"
            :local-filters="localFilterValues"
            @drill-down="handleDrillDown"
          />
        </template>
        <template #fallback>
          <ChartSkeleton />
        </template>
      </Suspense>
    </div>
  </div>
</template>

<script setup lang="ts">
// G2 函数式编程语法导入
import { Chart } from '@antv/g2'
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'

// G2Chart 组件基础实现示例（使用函数式语法）
const chartRef = ref<HTMLElement>()
let chart: Chart | null = null

// 响应式数据
const localFilterValues = ref<Record<string, any>>({})
const metrics = ref<MetricData[]>([])

// 计算属性：处理筛选后的数据
const filteredData = computed(() => {
  if (!chartData.value || Object.keys(localFilterValues.value).length === 0) {
    return chartData.value
  }

  return chartData.value.filter(item => {
    return Object.entries(localFilterValues.value).every(([key, value]) => {
      if (!value || (Array.isArray(value) && value.length === 0)) return true
      if (Array.isArray(value)) return value.includes(item[key])
      return item[key] === value
    })
  })
})

onMounted(() => {
  if (chartRef.value) {
    chart = new Chart({
      container: chartRef.value,
      autoFit: true,
    })

    // 使用 G2 函数式编程语法
    renderChart()
  }
})

// 使用函数式语法渲染图表
function renderChart() {
  if (!chart) return

  // 清空之前的图表
  chart.clear()

  // 根据图表类型使用函数式语法
  switch (config.type) {
    case 'bar':
      chart
        .interval()
        .data(filteredData.value)
        .encode('x', config.xField)
        .encode('y', config.yField)
        .encode('color', config.colorField)
        .scale('y', { nice: true })
        .interaction('elementHighlight')
        .interaction('tooltip', { shared: true })
      break

    case 'line':
      chart
        .line()
        .data(filteredData.value)
        .encode('x', config.xField)
        .encode('y', config.yField)
        .encode('color', config.colorField)
        .scale('x', { nice: true })
        .scale('y', { nice: true })
        .interaction('tooltip')
      break

    case 'pie':
      chart
        .interval()
        .data(filteredData.value)
        .transform({ type: 'stackY' })
        .coordinate({ type: 'theta', outerRadius: 0.8 })
        .encode('y', config.valueField)
        .encode('color', config.categoryField)
        .legend('color', { position: 'bottom' })
        .interaction('elementHighlight')
        .interaction('tooltip')
      break

    case 'area':
      chart
        .area()
        .data(filteredData.value)
        .encode('x', config.xField)
        .encode('y', config.yField)
        .encode('color', config.colorField)
        .style('fillOpacity', 0.6)
        .scale('x', { nice: true })
        .scale('y', { nice: true })
        .interaction('tooltip')
      break
  }

  // 添加数据下探交互
  if (config.drillDown?.enabled) {
    chart.interaction('elementClick', {
      callback: (event) => {
        const { data } = event.data
        handleDrillDown(data)
      }
    })
  }

  chart.render()
}

// 监听数据变化，重新渲染图表
watch([filteredData, () => config], () => {
  renderChart()
}, { deep: true })

// 局部筛选变化处理
function handleLocalFilterChange() {
  // 触发图表重新渲染
  renderChart()

  // 更新指标数据
  updateMetrics()
}

// 更新指标数据
function updateMetrics() {
  if (!config.showMetrics || !filteredData.value) return

  // 根据筛选后的数据计算指标
  metrics.value = config.metrics.map(metricConfig => {
    const value = calculateMetric(filteredData.value, metricConfig)
    return {
      ...metricConfig,
      value: formatMetricValue(value, metricConfig.format),
      trend: calculateTrend(value, metricConfig.previousValue),
      change: calculateChange(value, metricConfig.previousValue)
    }
  })
}

onUnmounted(() => {
  chart?.destroy()
})
</script>

<style lang="less" scoped>
.chart-widget {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .metrics-section {
    margin-bottom: 16px;

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 12px;

      .metric-card {
        text-align: center;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;

        .metric-value {
          font-size: 24px;
          font-weight: bold;
          color: #1890ff;
        }

        .metric-label {
          font-size: 12px;
          color: #666;
          margin-top: 4px;
        }

        .metric-trend {
          font-size: 12px;
          margin-top: 4px;

          &.up { color: #52c41a; }
          &.down { color: #ff4d4f; }
          &.stable { color: #666; }
        }
      }
    }
  }

  .local-filters {
    margin-bottom: 16px;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;
  }

  .chart-container {
    min-height: 300px;
    position: relative;
  }
}
</style>
```

### 2. 筛选器组件
```vue
<template>
  <div class="filter-panel">
    <a-row :gutter="16">
      <a-col :span="6">
        <a-range-picker 
          v-model:value="filters.dateRange"
          @change="handleFilterChange"
        />
      </a-col>
      <a-col :span="6">
        <a-select 
          v-model:value="filters.region"
          mode="multiple"
          placeholder="选择区域"
          @change="handleFilterChange"
        >
          <a-select-option v-for="region in regions" :key="region.value" :value="region.value">
            {{ region.label }}
          </a-select-option>
        </a-select>
      </a-col>
      <a-col :span="12">
        <slot name="custom-filters" :filters="filters" :onChange="handleFilterChange" />
      </a-col>
    </a-row>
  </div>
</template>
```

### 3. PDF导出功能
```typescript
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export class PDFExporter {
  async exportDashboard(elementId: string, filename: string) {
    const element = document.getElementById(elementId)
    if (!element) throw new Error('Element not found')
    
    // 设置导出样式
    element.classList.add('pdf-export-mode')
    
    try {
      // 生成Canvas
      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })
      
      // 创建PDF
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      })
      
      const imgData = canvas.toDataURL('image/png')
      const imgWidth = 297 // A4 landscape width
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      
      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight)
      pdf.save(`${filename}.pdf`)
    } finally {
      element.classList.remove('pdf-export-mode')
    }
  }
}
```

### 4. 图表懒加载实现
```typescript
// 使用 Vite 的动态导入实现代码分割
const G2Chart = defineAsyncComponent({
  loader: () => import('./G2Chart.vue'),
  loadingComponent: ChartSkeleton,
  errorComponent: ChartError,
  delay: 200,
  timeout: 3000
})

// Intersection Observer 实现可视区域懒加载
export function useChartLazyLoad() {
  const chartRefs = ref<HTMLElement[]>([])
  const loadedCharts = ref<Set<string>>(new Set())
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const chartId = entry.target.getAttribute('data-chart-id')
        if (chartId && !loadedCharts.value.has(chartId)) {
          loadedCharts.value.add(chartId)
          // 触发图表加载
          loadChart(chartId)
        }
      }
    })
  }, {
    rootMargin: '100px'
  })
  
  return { observer, loadedCharts }
}
```

## 🚀 实施计划

### 阶段一：AntV G2 函数式语法验证 (1-2天)
- [ ] 安装配置AntV G2依赖
- [ ] 研究并掌握G2函数式编程语法
- [ ] 开发G2Chart基础组件（使用函数式语法）
- [ ] 实现基本图表类型渲染（柱状图、折线图、饼图、面积图）
- [ ] 验证函数式语法在Vue 3环境下的集成效果
- [ ] 测试图表响应式和数据更新机制

### 阶段二：三区域图表组架构设计 (2-3天)
- [ ] 设计图表组三区域布局结构
- [ ] 实现数据指标区域组件
- [ ] 实现局部筛选区域组件
- [ ] 实现图表展示区域组件
- [ ] 建立三区域间的数据流和响应式联动
- [ ] 测试区域显示/隐藏的动态配置

### 阶段三：响应式图表组实现 (2-3天)
- [ ] 实现图表组的响应式布局系统
- [ ] 支持不同屏幕尺寸的自适应显示
- [ ] 实现指标卡片的响应式网格布局
- [ ] 优化局部筛选器的响应式排列
- [ ] 测试各种设备尺寸下的显示效果

### 阶段四：基础架构搭建 (2-3天)
- [ ] 创建看板页面基础组件结构
- [ ] 实现Tab切换和响应式布局
- [ ] 建立Vue 3组合式API状态管理机制
- [ ] 集成三区域图表组到看板架构中
- [ ] 实现图表组的配置化渲染

### 阶段五：数据联动与交互 (2-3天)
- [ ] 实现全局筛选器与图表组联动
- [ ] 实现局部筛选器与图表数据联动
- [ ] 实现指标数据的实时计算和更新
- [ ] 集成数据下探交互功能（使用G2函数式语法）
- [ ] 优化数据流管理和性能

### 阶段六：图表懒加载与性能优化 (2天)
- [ ] 实现图表组件的懒加载机制
- [ ] 使用Intersection Observer优化图表渲染
- [ ] 配置Vite代码分割和Tree Shaking
- [ ] 图表库单独分包优化
- [ ] 性能监控和优化

### 阶段七：导出功能实现 (2-3天)
- [ ] 集成html2canvas和jsPDF
- [ ] 实现包含三区域的完整PDF导出
- [ ] 优化导出样式和布局（特别是指标区域）
- [ ] 支持批量导出和自定义配置
- [ ] 测试导出功能的兼容性

### 阶段八：拖拽布局预留 (1-2天)
- [ ] 集成Vue Draggable Next
- [ ] 实现拖拽手柄和事件隔离
- [ ] 预留拖拽布局接口（考虑三区域结构）
- [ ] 测试G2函数式语法与拖拽的兼容性

### 阶段九：全面测试与优化 (2天)
- [ ] 全面功能测试（重点测试响应式和三区域联动）
- [ ] 性能压力测试
- [ ] 浏览器兼容性测试
- [ ] 用户体验优化和细节调整

## 📦 依赖安装

```bash
# 图表库
pnpm install @antv/g2

# PDF导出
pnpm install html2canvas jspdf

# 拖拽功能（预留）
pnpm install vue-draggable-next sortablejs
pnpm install -D @types/sortablejs

```

## 🎨 UI/UX设计要点

### 视觉设计
- **色彩方案**：基于Ant Design色彩体系，主色调使用品牌蓝
- **图表主题**：统一的图表配色方案，支持明暗主题切换
- **间距规范**：遵循8px网格系统，保持视觉一致性
- **字体层级**：清晰的信息层级，重要数据突出显示

### 交互设计
- **响应式布局**：适配1920px、1440px、1366px等主流分辨率
- **加载状态**：优雅的骨架屏和加载动画
- **错误处理**：友好的错误提示和重试机制
- **操作反馈**：及时的操作反馈和状态提示

### 可访问性
- **键盘导航**：支持Tab键导航
- **屏幕阅读器**：合理的ARIA标签
- **色彩对比度**：符合WCAG 2.1 AA标准

## 🔍 技术风险与应对

### 主要风险
1. **图表性能**：大量图表同时渲染可能影响性能
2. **PDF导出质量**：复杂图表导出可能失真
3. **浏览器兼容性**：Canvas和新特性的兼容性
4. **数据量限制**：大数据集的渲染性能

### 应对策略
1. **性能优化**：虚拟滚动、懒加载、防抖节流
2. **导出优化**：高DPI设置、SVG导出备选方案
3. **兼容性测试**：主流浏览器测试，Polyfill支持
4. **数据分页**：前端分页、服务端分页结合

## 📈 后续扩展规划

### 短期扩展 (1-2个月)
- [ ] 拖拽布局功能完整实现
- [ ] 更多图表类型支持
- [ ] 实时数据更新
- [ ] 移动端适配

### 中期扩展 (3-6个月)
- [ ] 图表联动和钻取
- [ ] 自定义图表配置界面
- [ ] 数据源管理
- [ ] 用户权限控制

### 长期扩展 (6个月以上)
- [ ] AI智能分析
- [ ] 协作和分享功能
- [ ] 多语言国际化
- [ ] 微前端架构升级

## 📝 总结

本方案基于现有技术栈，选择AntV G2作为核心图表库，通过组件化设计、配置化驱动、性能优化等手段，构建一个功能完整、性能优秀、可扩展的数据可视化看板平台。

**核心优势**：
- ✅ 技术栈统一，开发效率高
- ✅ 组件化设计，可维护性强
- ✅ 性能优化完善，用户体验好
- ✅ 扩展性良好，支持未来需求
- ✅ 兼容性验证，技术风险低

**下一步行动**：建议立即开始阶段一的基础架构搭建工作，为后续功能开发奠定坚实基础。